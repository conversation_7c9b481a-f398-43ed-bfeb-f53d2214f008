package com.polarion.synchronizer.proxy.feishu.translators;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Set;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.polarion.alm.tracker.ITestManagementService;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.ISynchronizationContext;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Attachment;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.TransferItem;
import com.polarion.synchronizer.proxy.feishu.FeishuProxy;
import com.polarion.synchronizer.proxy.feishu.FeishuRichText;
import com.polarion.synchronizer.proxy.feishu.translators.html2feishu.HTML2FeishuConverter;
import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;

/**
 * Polarion HTML 富文本到飞书富文本的转换器 参考 JIRA 的 PolarionToJiraDecriptionTranslator 实现模式
 */
public class PolarionToFeishuRichTextTranslator extends TypesafeTranslator<String, FeishuRichText, FeishuRichText> {

	@NotNull
	private final IProxy fromProxy;

	@NotNull
	private final ITestManagementService testManagementService;

	@NotNull
	private final String baseUrl;

	@NotNull
	private final ISynchronizationContext context;

	@NotNull
	private final ILogger logger;

	@NotNull
	private final Collection<ValueMapping> valueMappings;

	@Inject
	public PolarionToFeishuRichTextTranslator(@Assisted("fromProxy") @Nullable IProxy fromProxy,
			@Nullable ITestManagementService testManagementService, @Assisted Collection<ValueMapping> valueMappings,
			@NotNull ISynchronizationContext context) {
		super(String.class, FeishuRichText.class);
		this.fromProxy = fromProxy;
		this.testManagementService = testManagementService;
		this.valueMappings = valueMappings;
		this.context = context;
		this.logger = context.getLogger();

		// 获取基础 URL
		String systemBaseUrl = System.getProperty("base.url");
		this.baseUrl = systemBaseUrl != null ? systemBaseUrl.trim().replaceAll("/$", "") : "";
	}

	@Override
	public TranslationResult<FeishuRichText> translateUnidirectionalTypesafe(@Nullable String sourceValue,
			@Nullable FeishuRichText targetValue) {
		FeishuRichText convertedValue = convert(sourceValue);
		return createUnidirectionalResult(convertedValue, targetValue);
	}

	@Override
	public TranslationResult<FeishuRichText> translateBidirectionalTypesafe(@Nullable String sourceBaseline,
			@Nullable String sourceValue, @Nullable FeishuRichText targetBaseline,
			@Nullable FeishuRichText targetValue) {
		FeishuRichText convertedValue = convert(sourceValue);
		return createBidirectionalResult(sourceBaseline, sourceValue, convertedValue, targetBaseline, targetValue);
	}

	/**
	 * 转换 Polarion HTML 富文本为飞书富文本
	 */
	@Nullable
	private FeishuRichText convert(@Nullable String source) {
		if (source == null || source.trim().isEmpty()) {
			logger.debug("源 HTML 内容为空，返回空的飞书富文本");
			return FeishuRichText.createEmpty();
		}

		try {
			logger.debug("开始转换 Polarion HTML 到飞书富文本，内容长度: " + source.length());

			// 首先应用值映射（如果有配置）
			String processedSource = applyValueMappings(source);

			// 创建 HTML 到飞书格式的转换器
			HTML2FeishuConverter converter;

			// 如果fromProxy是FeishuProxy，传递飞书客户端和配置以支持附件处理
			if (fromProxy instanceof FeishuProxy) {
				FeishuProxy feishuProxy = (FeishuProxy) fromProxy;
				try {
					converter = new HTML2FeishuConverter((IPolarionProxy) fromProxy, testManagementService,
							baseUrl, logger, feishuProxy.getFeishuClient(), null, feishuProxy.getConfiguration());
				} catch (Exception e) {
					logger.warn("无法获取飞书客户端，使用简化转换器: " + e.getMessage());
					converter = new HTML2FeishuConverter((IPolarionProxy) fromProxy, testManagementService,
							baseUrl, logger);
				}
			} else {
				converter = new HTML2FeishuConverter((IPolarionProxy) fromProxy, testManagementService,
						baseUrl, logger);
			}

			// 转换 HTML 内容
			String convertedHTML = converter.convert(processedSource);

			// 创建飞书富文本对象
			FeishuRichText result = FeishuRichText.fromHTML(convertedHTML);

			// 设置已上传的附件列表，供后续清理使用
			result.setUploadedAttachmentNames(converter.getUploadedAttachments());

			// 记录已上传的附件信息，供后续处理使用
			// 注意：在Translator阶段无法直接修改TransferItem，需要在其他地方处理重复附件问题

			logger.debug("成功转换 Polarion HTML 到飞书富文本");
			return result;

		} catch (Exception e) {
			logger.warn("转换 Polarion HTML 到飞书富文本时发生错误: " + e.getMessage(), e);

			// 降级处理：创建纯文本版本
			try {
				String plainText = extractPlainTextFromHTML(source);
				return FeishuRichText.fromPlainText(plainText);
			} catch (Exception fallbackError) {
				logger.error("降级处理也失败了: " + fallbackError.getMessage(), fallbackError);
				return FeishuRichText.createEmpty();
			}
		}
	}

	/**
	 * 应用值映射配置
	 */
	@NotNull
	private String applyValueMappings(@NotNull String source) {
		if (valueMappings == null || valueMappings.isEmpty()) {
			return source;
		}

		String result = source;
		for (ValueMapping mapping : valueMappings) {
			if (mapping.getLeft() != null && mapping.getRight() != null) {
				String sourcePattern = mapping.getLeft();
				String targetValue = mapping.getRight();

				// 支持简单的字符串替换
				if (result.contains(sourcePattern)) {
					result = result.replace(sourcePattern, targetValue);
					logger.debug("应用值映射: " + sourcePattern + " -> " + targetValue);
				}
			}
		}

		return result;
	}

	/**
	 * 从 HTML 中提取纯文本
	 */
	@NotNull
	private String extractPlainTextFromHTML(@NotNull String html) {
		try {
			// 简单的 HTML 标签移除
			String plainText = html.replaceAll("<[^>]+>", "").replaceAll("&nbsp;", " ").replaceAll("&lt;", "<")
					.replaceAll("&gt;", ">").replaceAll("&amp;", "&").replaceAll("&quot;", "\"")
					.replaceAll("&#39;", "'").replaceAll("\\s+", " ").trim();

			return plainText.isEmpty() ? "" : plainText;

		} catch (Exception e) {
			logger.warn("从 HTML 提取纯文本失败: " + e.getMessage(), e);
			return html; // 失败时返回原始 HTML
		}
	}

	/**
	 * 检查源内容是否被修改（重写以处理 HTML 内容的特殊情况）
	 */
	@Override
	protected boolean isSourceModified(@Nullable String sourceValue, @Nullable String sourceBaseline) {
		// 对于 HTML 内容，我们需要进行更智能的比较
		if (sourceValue == null && sourceBaseline == null) {
			return false;
		}

		if (sourceValue == null || sourceBaseline == null) {
			return true;
		}

		// 标准化 HTML 内容后再比较
		String normalizedValue = normalizeHTML(sourceValue);
		String normalizedBaseline = normalizeHTML(sourceBaseline);

		boolean modified = !normalizedValue.equals(normalizedBaseline);

		if (modified) {
			logger.debug("检测到 HTML 内容变化");
		}

		return modified;
	}

	/**
	 * 标准化 HTML 内容以便比较
	 */
	@NotNull
	private String normalizeHTML(@NotNull String html) {
		return html.replaceAll("\\s+", " ").replaceAll(">\\s+<", "><").trim();
	}

	/**
	 * 从当前TransferItem中移除已上传的附件，避免重复处理
	 */
	private void removeUploadedAttachmentsFromTransferItem(@NotNull Set<String> uploadedAttachmentNames) {
		if (uploadedAttachmentNames.isEmpty()) {
			return;
		}

		try {
			// 获取当前正在处理的TransferItem
			TransferItem currentItem = FeishuProxy.getCurrentTransferItem();
			if (currentItem == null) {
				logger.debug("无法获取当前TransferItem，跳过附件清理");
				return;
			}

			// 获取附件列表
			@SuppressWarnings("unchecked")
			Collection<Attachment> attachments = (Collection<Attachment>) currentItem.getValue("attachments");
			if (attachments == null || attachments.isEmpty()) {
				logger.debug("当前TransferItem没有附件，跳过清理");
				return;
			}

			// 过滤掉已上传的附件
			Collection<Attachment> filteredAttachments = new ArrayList<>();
			int removedCount = 0;

			for (Attachment attachment : attachments) {
				if (uploadedAttachmentNames.contains(attachment.getFileName())) {
					logger.debug("移除已在富文本中上传的附件: " + attachment.getFileName());
					removedCount++;
				} else {
					filteredAttachments.add(attachment);
				}
			}

			if (removedCount > 0) {
				// 更新TransferItem的附件列表
				currentItem.put("attachments", filteredAttachments);
				logger.debug("从附件列表中移除了 " + removedCount + " 个已上传的附件");
			}

		} catch (Exception e) {
			logger.warn("清理已上传附件时发生异常", e);
		}
	}
}
