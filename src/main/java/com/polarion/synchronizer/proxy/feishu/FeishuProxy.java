package com.polarion.synchronizer.proxy.feishu;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.jetbrains.annotations.NotNull;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.field.builder.QueryProjectFieldsReq;
import com.lark.project.service.field.builder.QueryProjectFieldsResp;
import com.lark.project.service.field.model.FieldValuePair;
import com.lark.project.service.field.model.SimpleField;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeReq;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeResp;
import com.lark.project.service.workitem.builder.CreateWorkItemReq;
import com.lark.project.service.workitem.builder.CreateWorkItemResp;
import com.lark.project.service.workitem.builder.DeleteWorkItemReq;
import com.lark.project.service.workitem.builder.DeleteWorkItemResp;
import com.lark.project.service.workitem.builder.FilterReq;
import com.lark.project.service.workitem.builder.FilterResp;
import com.lark.project.service.workitem.builder.QueryWorkItemDetailReq;
import com.lark.project.service.workitem.builder.QueryWorkItemDetailResp;
import com.lark.project.service.workitem.builder.UpdateWorkItemReq;
import com.lark.project.service.workitem.builder.UpdateWorkItemResp;
import com.lark.project.service.workitem.model.WorkItemInfo;
import com.lark.project.service.workitem.model.WorkItemKeyType;
import com.polarion.core.util.StringUtils;
import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.SynchronizationException;
import com.polarion.synchronizer.model.CreateResult;
import com.polarion.synchronizer.model.FieldDefinition;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.Option;
import com.polarion.synchronizer.model.OptionFieldDefinition;
import com.polarion.synchronizer.model.TransferItem;
import com.polarion.synchronizer.model.UpdateResult;
import com.polarion.synchronizer.proxy.feishu.FeishuRichText;
import com.polarion.synchronizer.proxy.feishu.translators.FeishuRichTextHelper;
import com.polarion.synchronizer.proxy.feishu.FeishuAttachmentHandler;
import com.polarion.synchronizer.proxy.feishu.FeishuAttachment;
import com.lark.project.service.workitem.model.MultiTextDetail;
import com.polarion.synchronizer.model.Attachment;

/**
 * 飞书项目代理实现类 负责与飞书项目API的具体交互
 */
public class FeishuProxy implements IProxy {

	// 飞书项目字段常量
	public static final String KEY_WORK_ITEM_ID = "work_item_id";
	public static final String KEY_WORK_ITEM_TYPE_KEY = "work_item_type_key";
	public static final FieldDefinition FIELD_WORK_ITEM_ID = new FieldDefinition(KEY_WORK_ITEM_ID, "工作项ID",
			IProxy.TYPE_STRING, true, false);

	private static final Logger logger = Logger.getLogger(FeishuProxy.class);

	private final FeishuProxyConfiguration configuration;
	private final FeishuConnection connection;

	// 选项名称到ID的映射缓存
	private final Map<String, String> optionNameToIdCache = new ConcurrentHashMap<>();
	private final Map<String, String> optionIdToNameCache = new ConcurrentHashMap<>();

	// 附件缓存（参考 JIRA 的实现）
	private final Map<String, String> attachmentIdNameMap = new ConcurrentHashMap<>();

	// 附件处理器
	private FeishuAttachmentHandler attachmentHandler;



	public FeishuProxy(@NotNull FeishuProxyConfiguration configuration) {
		this.configuration = configuration;

		String configurationError = configuration.checkConfiguration();
		if (configurationError != null) {
			throw new SynchronizationException("飞书项目配置错误: " + configurationError + "。请检查飞书项目连接属性。");
		}

		// 初始化连接配置
		connection = (FeishuConnection) configuration.getConnection();
		if (connection == null) {
			throw new SynchronizationException("飞书项目连接配置不能为空");
		}

		logger.info("飞书项目代理初始化完成，项目Key: " + configuration.getProjectKey() + ", 认证模式: " + connection.getAuthMode());

		// 初始化附件处理器
		try {
			Client client = getClient();
			this.attachmentHandler = new FeishuAttachmentHandler(client.attachment(), configuration);
		} catch (Exception e) {
			logger.warn("初始化附件处理器失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 获取飞书API客户端实例
	 * @return 飞书客户端实例
	 * @throws SynchronizationException 如果无法获取客户端
	 */
	private Client getClient() {
		Client client = connection.getFeishuClient();
		if (client == null) {
			throw new SynchronizationException("无法获取飞书API客户端，请检查连接配置");
		}
		return client;
	}

	/**
	 * 根据访问凭证类型创建请求选项
	 */
	public RequestOptions getDefaultOptions() {
		String authMode = connection.getAuthMode();

		switch (authMode) {
		case "plugin_access_token":
			// 插件模式由SDK自动管理token，只需要设置X-User-Key
			RequestOptions.Builder builder = RequestOptions.newBuilder();
			String userKey = connection.getUserKey();
			if (userKey != null && !userKey.isEmpty()) {
				builder.userKey(userKey);
			}
			return builder.build();
		}

		return RequestOptions.newBuilder().build();
	}

	@Override
	public void close() {
		// 关闭资源，清理连接
		if (connection != null) {
			connection.clearClientCache();
			logger.debug("关闭飞书项目代理连接");
		}
	}

	@Override
	public List<UpdateResult> delete(List<String> itemIds) {
		List<UpdateResult> results = new ArrayList<>();

		if (itemIds == null || itemIds.isEmpty()) {
			return results;
		}

		logger.debug("开始删除工作项，数量: " + itemIds.size());

		for (String itemId : itemIds) {
			try {
				UpdateResult result = deleteSingleWorkItem(itemId);
				results.add(result);
			} catch (Exception e) {
				logger.error("删除工作项失败: " + itemId, e);
				results.add(new UpdateResult("删除失败: " + e.getMessage()));
			}
		}

		int successCount = (int) results.stream().mapToLong(r -> !r.hasError() ? 1 : 0).sum();
		int failCount = results.size() - successCount;

		logger.info("工作项删除完成，成功: " + successCount + "，失败: " + failCount);

		return results;
	}

	/**
	 * 删除单个工作项
	 */
	private UpdateResult deleteSingleWorkItem(String itemId) {
		try {
			logger.debug("删除工作项: " + itemId);

			// 首先需要获取工作项的类型，因为删除API需要工作项类型
			String workItemType = getWorkItemType(itemId);
			if (workItemType == null) {
				return new UpdateResult("无法获取工作项类型");
			}

			// 构建删除请求
			DeleteWorkItemReq req = DeleteWorkItemReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemTypeKey(workItemType).workItemID(Long.parseLong(itemId)).build();

			// 调用API删除工作项
			DeleteWorkItemResp resp = getClient().getWorkItemService().deleteWorkItem(req, getDefaultOptions());

			if (resp.success()) {
				logger.debug("工作项删除成功: " + itemId);
				return new UpdateResult(itemId);
			} else {
				logger.warn("工作项删除失败: " + itemId + ", 错误: " + resp.getErrMsg());
				return new UpdateResult(resp.getErrMsg());
			}

		} catch (NumberFormatException e) {
			logger.error("工作项ID格式错误: " + itemId, e);
			return new UpdateResult("工作项ID格式错误");
		} catch (Exception e) {
			logger.error("删除工作项时发生异常: " + itemId, e);
			return new UpdateResult("删除异常: " + e.getMessage());
		}
	}

	/**
	 * 获取工作项类型
	 */
	private String getWorkItemType(String itemId) {
		try {
			// 使用Filter API获取工作项基本信息来获取类型
			List<Long> workItemIds = new ArrayList<>();
			workItemIds.add(Long.parseLong(itemId));

			FilterReq req = FilterReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemIDs(workItemIds).pageNum(1L).pageSize(1L).build();

			FilterResp resp = getClient().getWorkItemService().filter(req, getDefaultOptions());

			if (resp.success() && resp.getData() != null && !resp.getData().isEmpty()) {
				WorkItemInfo workItem = resp.getData().get(0);
				return workItem.getWorkItemTypeKey();
			}

			logger.warn("无法获取工作项类型: " + itemId);
			return null;

		} catch (Exception e) {
			logger.error("获取工作项类型时发生错误: " + itemId, e);
			return null;
		}
	}

	@Override
	public String getContentScope() {
		// 返回内容范围，通常为null
		return null;
	}

	@Override
	public Collection<FieldDefinition> getDefinedFields(String typeId) {
		try {
			logger.debug("获取工作项类型字段定义: " + typeId);

			// 使用飞书项目API获取字段定义
			QueryProjectFieldsReq req = QueryProjectFieldsReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemTypeKey(typeId).build();

			QueryProjectFieldsResp resp = getClient().getFieldService().queryProjectFields(req, getDefaultOptions());

			if (!resp.success()) {
				logger.warn("获取字段定义失败: " + resp.getErrMsg());
				return getDefaultFieldDefinitions();
			}

			List<FieldDefinition> fieldDefinitions = new ArrayList<>();
			List<SimpleField> fields = resp.getData();

			logger.info("飞书API返回字段数量: " + (fields != null ? fields.size() : 0) + "，工作项类型: " + typeId);

			if (fields != null) {
				for (SimpleField field : fields) {
					if (field.getIsObsoleted() != null && field.getIsObsoleted()) {
						logger.debug("跳过已废弃字段: " + field.getFieldKey());
						continue; // 跳过已废弃的字段
					}

					String fieldAlias = field.getFieldAlias();
					String fieldKey = field.getFieldKey();
					String originalKey = StringUtils.isEmpty(fieldAlias) ? fieldKey : fieldAlias;
					String fieldName = field.getFieldName() != null ? field.getFieldName() + "(" + fieldKey + ")"
							: fieldKey;
					String fieldType = convertFeishuFieldTypeToPolarion(field.getFieldTypeKey());
					System.out.println("原始类型：" + field.getFieldTypeKey() + "转换后类型：" + fieldType);
					boolean isReadOnly = false; // 飞书项目字段一般都可编辑

					// 判断是否为多值字段
					// 基于字段类型和语义来判断，而不是仅仅基于是否有选项
					boolean isMultiValued = isMultiValuedField(fieldKey, field);

					// 判断是否为自定义字段
					boolean isCustomField = field.getIsCustomField() != null && field.getIsCustomField();

					String finalKey;
					if (isCustomField) {
						// 自定义字段保持原键名
						finalKey = originalKey;
						System.out.println(
								"typeid:" + typeId + ",key:" + originalKey + " label:" + fieldName + " (自定义字段)");
					} else {
						// 系统字段进行映射，将飞书字段映射为Polarion标准字段
						finalKey = findMappedKey(originalKey);
						System.out.println("typeid:" + typeId + ",key:" + originalKey + " label:" + fieldName
								+ " (系统字段) -> mapped:" + finalKey);
					}

					// 添加字段定义
					FieldDefinition fieldDef;
					if (field.getOptions() != null && !field.getOptions().isEmpty()) {
						// 为有选项的字段创建 OptionFieldDefinition
						List<Option> options = new ArrayList<>();
						for (com.lark.project.service.field.model.Option option : field.getOptions()) {
							if (option.getValue() != null && option.getLabel() != null) {
								options.add(new Option(option.getValue(), option.getLabel()));
								// 构建选项名称到ID的映射缓存
								updateOptionCache(option.getLabel(), option.getValue());
							}
						}
						fieldDef = new OptionFieldDefinition(finalKey, fieldName, fieldType, isReadOnly,
								isMultiValued, options);
					} else {
						fieldDef = new FieldDefinition(finalKey, fieldName, fieldType, isReadOnly, isMultiValued);
					}
					fieldDefinitions.add(fieldDef);
				}
			}

			// 添加必需的 Polarion 同步器字段
			addRequiredSynchronizerFields(fieldDefinitions);

			// 确保所有类型都有基本的系统字段（用于通用字段计算）
			ensureBasicSystemFields(fieldDefinitions);

			logger.info("最终返回字段数量: " + fieldDefinitions.size() + "，工作项类型: " + typeId);
			return fieldDefinitions;

		} catch (Exception e) {
			logger.error("获取字段定义时发生错误，工作项类型: " + typeId, e);
			return getDefaultFieldDefinitions();
		}
	}

	@Override
	public Collection<Option> getDefinedTypes() {
		try {
			logger.debug("获取工作项类型列表");
			ListProjectWorkItemTypeReq req = ListProjectWorkItemTypeReq.newBuilder()
					.projectKey(configuration.getProjectKey()).build();

			// 使用飞书项目API获取工作项类型
			ListProjectWorkItemTypeResp resp = getClient().getProjectService().listProjectWorkItemType(req,
					getDefaultOptions());

			if (!resp.success()) {
				throw new SynchronizationException("获取飞书工作项类型失败" + resp.getErrMsg());
			}

			List<Option> options = new ArrayList<>();
			List<WorkItemKeyType> types = resp.getData();

			boolean excludeChart = configuration.isExcludeChartType();

			if (types != null) {
				for (WorkItemKeyType type : types) {
					if (type.getIsDisable() != null && type.getIsDisable() == 1) {
						continue; // 跳过已禁用的类型
					}

					String typeKey = type.getTypeKey();
					String typeName = type.getName() != null ? type.getName() : typeKey;

					// 如果配置了排除图表类型，则跳过chart类型
					if (excludeChart && "chart".equals(typeKey)) {
						logger.debug("跳过图表类型: " + typeKey);
						continue;
					}

					Option option = new Option(typeKey, typeName);
					options.add(option);
				}
			}

			String excludeInfo = excludeChart ? "（排除了图表类型）" : "";
			logger.info("获取到 " + options.size() + " 个工作项类型" + excludeInfo);
			return options;

		} catch (Exception e) {
			throw new SynchronizationException("获取飞书工作项类型失败", e);
		}
	}

	@Override
	public Collection<Option> getDefinedTypes(Collection<String> requiredTypes) {
		Collection<Option> allTypes = getDefinedTypes();

		if (requiredTypes == null || requiredTypes.isEmpty()) {
			return allTypes;
		}

		// 过滤出需要的类型
		List<Option> filteredTypes = new ArrayList<>();
		Set<String> requiredTypeSet = new HashSet<>(requiredTypes);

		for (Option option : allTypes) {
			if (requiredTypeSet.contains(option.getId())) {
				filteredTypes.add(option);
			}
		}

		logger.debug("过滤后的工作项类型数量: " + filteredTypes.size());
		return filteredTypes;
	}

	@Override
	public Collection<TransferItem> getItems(Collection<String> itemIds, Collection<String> fieldKeys) {
		try {
			logger.debug("获取指定工作项，数量: " + (itemIds != null ? itemIds.size() : 0));

			if (itemIds == null || itemIds.isEmpty()) {
				return new ArrayList<>();
			}

			List<TransferItem> items = new ArrayList<>();

			// 将字符串ID转换为Long类型
			List<Long> workItemIds = new ArrayList<>();
			for (String itemId : itemIds) {
				try {
					workItemIds.add(Long.parseLong(itemId));
				} catch (NumberFormatException e) {
					logger.warn("无效的工作项ID: " + itemId);
				}
			}

			if (workItemIds.isEmpty()) {
				return items;
			}

			// 第一步：使用Filter API获取工作项基本信息（包含类型）
			FilterReq filterReq = FilterReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemIDs(workItemIds).pageNum(1L).pageSize((long) workItemIds.size()).build();

			FilterResp filterResp = getClient().getWorkItemService().filter(filterReq, getDefaultOptions());

			if (!filterResp.success()) {
				logger.warn("获取工作项基本信息失败: " + filterResp.getErrMsg());
				return items;
			}

			List<WorkItemInfo> basicWorkItems = filterResp.getData();
			if (basicWorkItems == null || basicWorkItems.isEmpty()) {
				logger.warn("未找到指定的工作项");
				return items;
			}

			// 第二步：按工作项类型分组
			Map<String, List<Long>> typeToIdsMap = new HashMap<>();
			for (WorkItemInfo workItem : basicWorkItems) {
				String typeKey = workItem.getWorkItemTypeKey();
				if (typeKey != null) {
					typeToIdsMap.computeIfAbsent(typeKey, k -> new ArrayList<>()).add(workItem.getID());
				}
			}

			// 第三步：按类型批量查询详细信息
			for (Map.Entry<String, List<Long>> entry : typeToIdsMap.entrySet()) {
				String workItemType = entry.getKey();
				List<Long> idsOfType = entry.getValue();

				try {
					QueryWorkItemDetailReq detailReq = QueryWorkItemDetailReq.newBuilder()
							.projectKey(configuration.getProjectKey())
							.workItemTypeKey(workItemType)
							.workItemIDs(idsOfType)
							.fields(fieldKeys != null ? new ArrayList<>(fieldKeys) : null)
							.build();

					QueryWorkItemDetailResp detailResp = getClient().getWorkItemService().queryWorkItemDetail(detailReq, getDefaultOptions());

					if (detailResp.success() && detailResp.getData() != null) {
						for (WorkItemInfo workItem : detailResp.getData()) {
							TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
							if (item != null) {
								// 填充附件内容（参考 JIRA 的实现）
								fillAttachments(item);
								items.add(item);
							}
						}
					} else {
						logger.warn("获取工作项类型 " + workItemType + " 的详细信息失败: " +
								(detailResp.getErrMsg() != null ? detailResp.getErrMsg() : "未知错误"));
					}
				} catch (Exception e) {
					logger.error("查询工作项类型 " + workItemType + " 时发生异常", e);
				}
			}

			logger.info("成功获取 " + items.size() + " 个工作项");
			return items;

		} catch (Exception e) {
			logger.error("获取指定工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Collection<TransferItem> getScopeItems(Collection<String> fieldKeys) {
		try {
			logger.debug("获取范围内所有工作项");

			List<TransferItem> allItems = new ArrayList<>();

			// 使用Filter API获取工作项列表
			FilterReq req = FilterReq.newBuilder().projectKey(configuration.getProjectKey()).pageNum(1L).pageSize(100L) // 每页100个工作项
					.build();

			// 如果配置了工作项类型过滤，添加类型过滤
			if (configuration.getWorkItemTypes() != null && !configuration.getWorkItemTypes().isEmpty()) {
				String[] typeArray = configuration.getWorkItemTypes().split(",");
				List<String> typeList = new ArrayList<>();
				for (String type : typeArray) {
					typeList.add(type.trim());
				}
				req.getFilterReqBody().setWorkItemTypeKeys(typeList);
			}

			// 分页获取所有工作项
			int currentPage = 1;
			boolean hasMore = true;

			while (hasMore) {
				req.getFilterReqBody().setPageNum((long) currentPage);

				FilterResp resp = getClient().getWorkItemService().filter(req, getDefaultOptions());

				if (!resp.success()) {
					logger.warn("获取工作项列表失败: " + resp.getErrMsg());
					break;
				}

				List<WorkItemInfo> workItems = resp.getData();
				if (workItems != null && !workItems.isEmpty()) {
					for (WorkItemInfo workItem : workItems) {
						TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
						if (item != null) {
							// 填充附件内容（参考 JIRA 的实现）
							fillAttachments(item);
							allItems.add(item);
						}
					}

					// 检查是否还有更多页面
					if (resp.getPagination() != null) {
						// 检查分页信息，如果当前页数量等于页面大小，可能还有更多
						hasMore = workItems.size() >= 100;
					} else {
						hasMore = workItems.size() >= 100; // 如果返回的数量等于页面大小，可能还有更多
					}

					currentPage++;
				} else {
					hasMore = false;
				}
			}

			logger.info("成功获取 " + allItems.size() + " 个范围内工作项");
			return allItems;

		} catch (Exception e) {
			logger.error("获取范围内工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public String getTargetName() {
		// 返回目标系统名称
		return "飞书项目 - " + configuration.getProjectKey();
	}

	@Override
	public boolean hasNonSynchronizableFields() {
		// 飞书项目的字段一般都可以同步
		return false;
	}

	@Override
	public boolean isHierarchySupported() {
		// 飞书项目不支持层次结构
		return false;
	}

	@Override
	public List<UpdateResult> update(List<TransferItem> items) {
		List<UpdateResult> results = new ArrayList<>();

		if (items == null || items.isEmpty()) {
			return results;
		}

		logger.debug("开始处理工作项，数量: " + items.size());

		for (TransferItem item : items) {
			try {
				UpdateResult result;

				// 判断是创建还是更新
				if (item.getId() == null || item.getKey().isForeign) {
					// 创建新工作项
					result = createSingleWorkItem(item);
				} else {
					// 更新现有工作项
					result = updateSingleWorkItem(item);
				}

				results.add(result);
			} catch (Exception e) {
				logger.error("处理工作项失败: " + item.getId(), e);
				results.add(new UpdateResult("处理失败: " + e.getMessage()));
			}
		}

		int successCount = (int) results.stream().mapToLong(r -> !r.hasError() ? 1 : 0).sum();
		int failCount = results.size() - successCount;

		logger.info("工作项处理完成，成功: " + successCount + "，失败: " + failCount);

		return results;
	}

	/**
	 * 创建单个工作项
	 */
	private UpdateResult createSingleWorkItem(TransferItem item) {
		try {
			logger.debug("创建新工作项，类型: " + item.getType());

			// 验证必需字段
			String workItemType = item.getType();
			if (workItemType == null || workItemType.trim().isEmpty()) {
				workItemType = "task"; // 默认类型为task
				logger.debug("工作项类型为空，使用默认类型: " + workItemType);
			}

			String workItemTitle = (String) item.getValue("title");
			if (workItemTitle == null || workItemTitle.trim().isEmpty()) {
				workItemTitle = (String) item.getValue("name");
				if (workItemTitle == null || workItemTitle.trim().isEmpty()) {
					workItemTitle = "新工作项"; // 默认标题
					logger.debug("工作项标题为空，使用默认标题: " + workItemTitle);
				}
			}

			// 验证工作项类型是否有效
			if (!isValidWorkItemType(workItemType)) {
				logger.warn("无效的工作项类型: " + workItemType + "，使用默认类型: task");
				workItemType = "task";
			}

			// 构建创建请求
			CreateWorkItemReq req = CreateWorkItemReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemTypeKey(workItemType).name(workItemTitle).build();

			// 设置字段值
			List<FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 设置描述
			String description = (String) item.getValue("description");
			if (description != null) {
				FieldValuePair descField = new FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(description);
				fieldValuePairs.add(descField);
			}

			// 设置自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						String fieldKey = entry.getKey();
						Object fieldValue = entry.getValue();

						// 跳过只读字段和系统字段
						if (IProxy.KEY_ITEM_URL.equals(fieldKey) || KEY_WORK_ITEM_ID.equals(fieldKey)
								|| "created_at".equals(fieldKey) || "updated_at".equals(fieldKey)
								|| "creator".equals(fieldKey)) {
							continue;
						}

						// 验证字段值
						if (!isValidFieldValue(fieldKey, fieldValue)) {
							logger.warn("跳过无效字段值: " + fieldKey + " = " + fieldValue);
							continue;
						}

						Object convertedValue = convertPolarionValueToFeishu(fieldValue);
						if (convertedValue != null) {
							FieldValuePair customField = new FieldValuePair();
							customField.setFieldKey(fieldKey);
							customField.setFieldValue(convertedValue);
							fieldValuePairs.add(customField);
						}
					}
				}
			}

			req.getCreateWorkItemReqBody().setFieldValuePairs(fieldValuePairs);

			// 调用API创建工作项
			CreateWorkItemResp resp = getClient().getWorkItemService().createWorkItem(req, getDefaultOptions());

			if (resp.success()) {
				Long createdId = resp.getData();
				String createdIdStr = createdId != null ? createdId.toString() : null;

				// 更新TransferItem的ID
				item.setId(createdIdStr);

				// 处理附件上传（参考 JIRA 的实现）
				@SuppressWarnings("unchecked")
				Collection<Attachment> attachments = (Collection<Attachment>) item.getValue("attachments");
				if (attachments != null && !attachments.isEmpty()) {
					logger.debug("开始处理附件上传，数量: " + attachments.size());
					UpdateResult attachmentResult = processAttachments(createdIdStr, workItemType, attachments);
					if (attachmentResult.hasError()) {
						logger.warn("附件上传失败: " + attachmentResult.getError());
						// 注意：即使附件上传失败，工作项创建仍然成功
					}
				}

				logger.debug("工作项创建成功: " + createdIdStr);
				return new CreateResult(createdIdStr, null);
			} else {
				logger.warn("工作项创建失败，错误: " + resp.getErrMsg());
				return new CreateResult(null, resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("创建工作项时发生异常", e);
			return new CreateResult(null, "创建异常: " + e.getMessage());
		}
	}

	/**
	 * 更新单个工作项
	 */
	private UpdateResult updateSingleWorkItem(TransferItem item) {
		try {
			// 构建更新请求
			UpdateWorkItemReq req = UpdateWorkItemReq.newBuilder().projectKey(configuration.getProjectKey())
					.workItemTypeKey(item.getType() != null ? item.getType() : "")
					.workItemID(Long.parseLong(item.getId())).build();

			// 设置更新字段
			List<FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 更新标题
			String title = (String) item.getValue("title");
			if (title == null) {
				title = (String) item.getValue("name");
			}
			if (title != null) {
				FieldValuePair titleField = new FieldValuePair();
				titleField.setFieldKey("name");
				titleField.setFieldValue(title);
				fieldValuePairs.add(titleField);
			}

			// 更新描述
			String description = (String) item.getValue("description");
			if (description != null) {
				FieldValuePair descField = new FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(description);
				fieldValuePairs.add(descField);
			}

			// 更新自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						String fieldKey = entry.getKey();
						Object fieldValue = entry.getValue();

						// 跳过只读字段和系统字段
						if (IProxy.KEY_ITEM_URL.equals(fieldKey) || KEY_WORK_ITEM_ID.equals(fieldKey)
								|| "created_at".equals(fieldKey) || "updated_at".equals(fieldKey)
								|| "creator".equals(fieldKey)) {
							continue;
						}

						// 验证字段值
						if (!isValidFieldValue(fieldKey, fieldValue)) {
							logger.warn("跳过无效字段值: " + fieldKey + " = " + fieldValue);
							continue;
						}

						Object convertedValue = convertPolarionValueToFeishu(fieldValue);
						if (convertedValue != null) {
							FieldValuePair customField = new FieldValuePair();
							customField.setFieldKey(fieldKey);
							customField.setFieldValue(convertedValue);
							fieldValuePairs.add(customField);
						}
					}
				}
			}

			req.getUpdateWorkItemReqBody().setUpdateFields(fieldValuePairs);

			// 调用API更新工作项
			UpdateWorkItemResp resp = getClient().getWorkItemService().updateWorkItem(req, getDefaultOptions());

			if (resp.success()) {
				// 处理附件上传（参考 JIRA 的实现）
				@SuppressWarnings("unchecked")
				Collection<Attachment> attachments = (Collection<Attachment>) item.getValue("attachments");
				if (attachments != null && !attachments.isEmpty()) {
					logger.debug("开始处理附件上传，数量: " + attachments.size());
					String workItemType = item.getType() != null ? item.getType() : "";
					UpdateResult attachmentResult = processAttachments(item.getId(), workItemType, attachments);
					if (attachmentResult.hasError()) {
						logger.warn("附件上传失败: " + attachmentResult.getError());
						// 注意：即使附件上传失败，工作项更新仍然成功
					}
				}

				logger.debug("工作项更新成功: " + item.getId());
				return UpdateResult.success();
			} else {
				logger.warn("工作项更新失败: " + item.getId() + ", 错误: " + resp.getErrMsg());
				return new UpdateResult(resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("更新工作项时发生异常: " + item.getId(), e);
			return new UpdateResult("更新异常: " + e.getMessage());
		}
	}

	/**
	 * 将飞书项目字段类型转换为Polarion字段类型
	 */
	private String convertFeishuFieldTypeToPolarion(String feishuFieldType) {
		if (feishuFieldType == null) {
			return IProxy.TYPE_STRING;
		}

		switch (feishuFieldType.toLowerCase()) {
		case "multi_file":
			return IProxy.TYPE_ATTACHMENT;
		case "text": // feishu 标题
			return IProxy.TYPE_STRING;

		case "multi_text": // feishu 富文本描述
			return "feishu:rich-text"; // 使用自定义的飞书富文本类型
		case "rich_text":
		case "richtext":
			return IProxy.TYPE_RICH_TEXT;
		case "number":
		case "integer":
			return IProxy.TYPE_INTEGER;
		case "float":
		case "decimal":
			return IProxy.TYPE_FLOAT;
		case "date":
			return IProxy.TYPE_DATE;
		case "datetime":
		case "timestamp":
			return IProxy.TYPE_DATE_TIME;
		case "bool":
			return IProxy.TYPE_BOOLEAN;
		case "multi_user":
		case "user":
			return "feishu:user";
		case "select":
		case "multi_select":
			return IProxy.TYPE_OPTION; // 多选也使用option类型，通过isMultiValued区分
		case "work_item_template":
		case "owned_project":
		case "template_type":
		case "work_item_status":
			return IProxy.TYPE_OPTION;
		case "work_item_related_multi_select":
		case "work_item_related_select":
			return IProxy.TYPE_RELATION;
		case "link":
			return IProxy.TYPE_ITEM_REF_HYPERLINK_HYPERLINK;
		default:
			logger.warn("未知的飞书字段类型: " + feishuFieldType + "，使用默认类型string");
			return IProxy.TYPE_STRING;
		}
	}

	/**
	 * 判断字段是否为多值字段
	 */
	private boolean isMultiValuedField(String fieldKey, SimpleField field) {
		// 有选项的字段，基于字段语义判断
		switch (fieldKey) {
			case KEY_WORK_ITEM_TYPE_KEY:
			case "multi_text":
				return false; // 单值选项字段
			default:
				// 对于未知字段，如果有多个选项且字段名包含复数形式，认为是多值
				return field.getOptions() != null && field.getOptions().size() > 1 && fieldKey.contains("multi");
		}
	}

	/**
	 * 将飞书系统字段映射为Polarion标准字段 只映射那些确实对应Polarion标准字段的字段，其他保持原名
	 */
	private String findMappedKey(String feishuFieldKey) {
		if (feishuFieldKey == null) {
			return feishuFieldKey;
		}

		// 只映射确定的Polarion标准字段
		switch (feishuFieldKey) {
		case "name":
			return "title"; // 飞书的name映射为Polarion的title
		case "description":
			return "description"; // 描述字段保持一致
		case "work_item_status":
			return "status"; // 状态字段
		case "priority":
			return "priority"; // 优先级字段保持一致
		case "current_status_operator":
			return "assignee"; // 当前负责人映射为指派人
		case "owner":
			return "author"; // 创建人映射为作者
		case "start_time":
			return "created"; // 创建时间
		case "updated_at":
			return "updated"; // 更新时间
		case "updated_by":
			return "updatedBy"; // 更新人
		case "finish_time":
			return "dueDate"; // 截止日期
		case "work_item_id":
			return KEY_WORK_ITEM_ID; // 工作项ID
		case KEY_WORK_ITEM_TYPE_KEY:
			return IProxy.KEY_TYPE; // 类型字段
		case "issue_reporter":
			return "reporter"; // 报告人
		case "tags":
			return "categories"; // 标签映射为分类
		case "watchers":
			return "subscribers"; // 关注人映射为订阅者
		default:
			// 其他字段（包括系统字段）保持原样，让用户手动配置映射
			return feishuFieldKey;
		}
	}

	/**
	 * 添加必需的Polarion同步器字段
	 */
	private void addRequiredSynchronizerFields(List<FieldDefinition> fieldDefinitions) {
		// 创建已存在字段的key集合，用于去重
		Set<String> existingFieldKeys = fieldDefinitions.stream().map(FieldDefinition::getKey)
				.collect(java.util.stream.Collectors.toSet());

		// 添加必需的同步器字段（如果不存在）
		if (!existingFieldKeys.contains(KEY_WORK_ITEM_ID)) {
			fieldDefinitions.add(FIELD_WORK_ITEM_ID);
		}

		if (!existingFieldKeys.contains(IProxy.FIELD_ITEM_URL.getKey())) {
			fieldDefinitions.add(IProxy.FIELD_ITEM_URL);
		}
	}

	/**
	 * 确保所有类型都有基本的系统字段 这样可以保证通用字段计算时有更多的共同字段
	 */
	private void ensureBasicSystemFields(List<FieldDefinition> fieldDefinitions) {
		// 创建已存在字段的key集合，用于去重
		Set<String> existingFieldKeys = fieldDefinitions.stream().map(FieldDefinition::getKey)
				.collect(java.util.stream.Collectors.toSet());

		// 添加基本系统字段（如果不存在）
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "title", "标题", IProxy.TYPE_STRING, false, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "description", "描述", IProxy.TYPE_RICH_TEXT, false,
				false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "status", "状态", IProxy.TYPE_OPTION, false, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "priority", "优先级", IProxy.TYPE_OPTION, false, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "assignee", "指派人", "feishu:user", false, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "author", "创建人", "feishu:user", true, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "created", "创建时间", IProxy.TYPE_DATE_TIME, true, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "updated", "更新时间", IProxy.TYPE_DATE_TIME, true, false);
		addFieldIfMissing(fieldDefinitions, existingFieldKeys, "updatedBy", "更新人", "feishu:user", true, false);
		// 为 type 字段添加选项
		if (!existingFieldKeys.contains(IProxy.KEY_TYPE)) {
			try {
				Collection<Option> typeOptions = getDefinedTypes();
				fieldDefinitions.add(new OptionFieldDefinition(IProxy.KEY_TYPE, "类型", IProxy.TYPE_OPTION, false, false, typeOptions));
			} catch (Exception e) {
				logger.warn("无法获取工作项类型选项，使用普通字段定义", e);
				addFieldIfMissing(fieldDefinitions, existingFieldKeys, IProxy.KEY_TYPE, "类型", IProxy.TYPE_OPTION, false, false);
			}
		}
	}

	/**
	 * 如果字段不存在则添加
	 */
	private void addFieldIfMissing(List<FieldDefinition> fieldDefinitions, Set<String> existingKeys, String key,
			String label, String type, boolean readOnly, boolean multiValued) {
		if (!existingKeys.contains(key)) {
			fieldDefinitions.add(new FieldDefinition(key, label, type, readOnly, multiValued));
		}
	}

	/**
	 * 获取默认字段定义（当API调用失败时使用）
	 */
	private Collection<FieldDefinition> getDefaultFieldDefinitions() {
		List<FieldDefinition> fieldDefinitions = new ArrayList<>();

		// 添加必需的同步器字段
		addRequiredSynchronizerFields(fieldDefinitions);

		// 添加一些常用的基础字段（使用映射后的键名）
		fieldDefinitions.add(new FieldDefinition("title", "标题", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("description", "描述", IProxy.TYPE_RICH_TEXT, false, false));
		fieldDefinitions.add(new FieldDefinition("status", "状态", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("priority", "优先级", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("assignee", "指派人", "feishu:user", false, false));
		fieldDefinitions.add(new FieldDefinition("author", "创建人", "feishu:user", true, false));
		fieldDefinitions.add(new FieldDefinition("created", "创建时间", IProxy.TYPE_DATE_TIME, true, false));
		fieldDefinitions.add(new FieldDefinition("updated", "更新时间", IProxy.TYPE_DATE_TIME, true, false));
		// 为 type 字段添加选项
		try {
			Collection<Option> typeOptions = getDefinedTypes();
			fieldDefinitions.add(new OptionFieldDefinition(IProxy.KEY_TYPE, "类型", IProxy.TYPE_OPTION, false, false, typeOptions));
		} catch (Exception e) {
			logger.warn("无法获取工作项类型选项，使用普通字段定义", e);
			fieldDefinitions.add(new FieldDefinition(IProxy.KEY_TYPE, "类型", IProxy.TYPE_OPTION, false, false));
		}

		logger.info("使用默认字段定义，共 " + fieldDefinitions.size() + " 个字段");
		return fieldDefinitions;
	}

	/**
	 * 更新选项名称到ID的缓存
	 */
	private void updateOptionCache(@NotNull String optionName, @NotNull String optionId) {
		optionNameToIdCache.put(optionName, optionId);
		optionIdToNameCache.put(optionId, optionName);
		logger.debug("更新选项缓存: " + optionName + " <-> " + optionId);
	}

	/**
	 * 根据选项名称获取选项ID
	 */
	public String getOptionIdByName(@NotNull String optionName) {
		return optionNameToIdCache.get(optionName);
	}

	/**
	 * 根据选项ID获取选项名称
	 */
	public String getOptionNameById(@NotNull String optionId) {
		return optionIdToNameCache.get(optionId);
	}

	/**
	 * 清空选项缓存
	 */
	public void clearOptionCache() {
		optionNameToIdCache.clear();
		optionIdToNameCache.clear();
		logger.debug("清空选项缓存");
	}

	/**
	 * 创建附件ID映射（参考 JIRA 的实现）
	 */
	private void createAttachmentIdMap(@NotNull Object value, @NotNull String key) {
		if (value instanceof Map) {
			@SuppressWarnings("unchecked")
			Map<String, Object> valueMap = (Map<String, Object>) value;
			if ("attachments".equals(key) || "files".equals(key)) {
				String id = (String) valueMap.get("id");
				String filename = (String) valueMap.get("filename");
				if (id != null && filename != null) {
					attachmentIdNameMap.put(id, filename);
					logger.debug("添加附件映射: " + id + " -> " + filename);
				}
			}
		}
	}

	/**
	 * 获取附件ID到名称的映射
	 */
	@NotNull
	public Map<String, String> getAttachmentIdNameMap() {
		return new HashMap<>(attachmentIdNameMap);
	}

	/**
	 * 清空附件缓存
	 */
	public void clearAttachmentCache() {
		attachmentIdNameMap.clear();
		if (attachmentHandler != null) {
			attachmentHandler.clearAttachmentMapping();
		}
		logger.debug("清空附件缓存");
	}

	/**
	 * 处理附件上传（参考 JIRA 的实现）
	 */
	@NotNull
	private UpdateResult processAttachments(@NotNull String workItemId,
	                                       @NotNull String workItemTypeKey,
	                                       @NotNull Collection<Attachment> attachments) {
		if (attachmentHandler == null) {
			logger.warn("附件处理器未初始化，跳过附件处理");
			return UpdateResult.success();
		}

		return attachmentHandler.processAttachments(workItemId, workItemTypeKey, attachments);
	}

	/**
	 * 处理附件上传并清理已上传的附件
	 */
	@NotNull
	private UpdateResult processAttachments(@NotNull String workItemId,
	                                       @NotNull String workItemTypeKey,
	                                       @NotNull Collection<Attachment> attachments,
	                                       @NotNull Set<String> uploadedAttachmentNames) {
		if (attachmentHandler == null) {
			logger.warn("附件处理器未初始化，跳过附件处理");
			return UpdateResult.success();
		}

		// 过滤掉已经在富文本中上传的附件
		Collection<Attachment> filteredAttachments = new ArrayList<>();
		for (Attachment attachment : attachments) {
			if (!uploadedAttachmentNames.contains(attachment.getFileName())) {
				filteredAttachments.add(attachment);
			} else {
				logger.debug("跳过已在富文本中上传的附件: " + attachment.getFileName());
			}
		}

		if (filteredAttachments.isEmpty()) {
			logger.debug("所有附件都已在富文本中上传，跳过附件处理");
			return UpdateResult.success();
		}

		logger.debug("处理剩余附件，数量: " + filteredAttachments.size());
		return attachmentHandler.processAttachments(workItemId, workItemTypeKey, filteredAttachments);
	}

	/**
	 * 填充附件内容（参考 JIRA 的 fillAttachments 方法）
	 */
	private void fillAttachments(@NotNull TransferItem transferItem) {
		@SuppressWarnings("unchecked")
		Collection<FeishuAttachment> feishuAttachments = (Collection<FeishuAttachment>) transferItem.getValue("attachments");

		if (feishuAttachments != null && !feishuAttachments.isEmpty()) {
			Collection<Attachment> withContent = new ArrayList<>(feishuAttachments.size());
			String workItemId = transferItem.getId();
			String workItemType = transferItem.getType();

			for (FeishuAttachment feishuAttachment : feishuAttachments) {
				try {
					// 创建带内容的附件对象（使用延迟加载）
					if (attachmentHandler != null) {
						Attachment attachment = attachmentHandler.downloadAttachment(
							feishuAttachment.getFileId(),
							feishuAttachment.getFileName(),
							workItemId,
							workItemType
						);

						if (attachment != null) {
							withContent.add(attachment);
							logger.debug("添加附件: " + feishuAttachment.getFileName());
						} else {
							logger.warn("无法下载附件: " + feishuAttachment.getFileName());
						}
					} else {
						logger.warn("附件处理器未初始化，无法下载附件: " + feishuAttachment.getFileName());
					}
				} catch (Exception e) {
					logger.error("处理附件失败: " + feishuAttachment.getFileName(), e);
				}
			}

			// 替换为带内容的附件集合
			transferItem.put("attachments", withContent);
			logger.debug("填充附件内容完成，数量: " + withContent.size());
		}
	}

	/**
	 * 将Polarion字段值转换为飞书项目字段值
	 */
	private Object convertPolarionValueToFeishu(Object polarionValue) {
		if (polarionValue == null) {
			return null;
		}

		try {
			// 处理日期类型
			if (polarionValue instanceof Date) {
				Date date = (Date) polarionValue;
				return date.getTime() / 1000; // 飞书使用秒级时间戳
			}

			// 处理布尔类型
			if (polarionValue instanceof Boolean) {
				return polarionValue;
			}

			// 处理数字类型
			if (polarionValue instanceof Number) {
				return polarionValue;
			}

			// 处理飞书富文本类型
			if (polarionValue instanceof FeishuRichText) {
				FeishuRichText feishuRichText = (FeishuRichText) polarionValue;
				return FeishuRichTextHelper.toMultiTextDetail(feishuRichText);
			}

			// 处理字符串类型
			if (polarionValue instanceof String) {
				String strValue = (String) polarionValue;

				// 检查是否是选项值格式
				if (strValue.contains("|") || strValue.startsWith("option:")) {
					return convertOptionValue(strValue);
				}

				// 对于选项字段，尝试通过名称查找ID
				String optionId = getOptionIdByName(strValue);
				if (optionId != null) {
					logger.debug("通过选项名称找到ID: " + strValue + " -> " + optionId);
					return optionId;
				}

				// 普通字符串值（包括用户字段，让Translator处理）
				return strValue;
			}

			// 处理集合类型
			if (polarionValue instanceof Collection) {
				Collection<?> collection = (Collection<?>) polarionValue;
				List<Object> convertedList = new ArrayList<>();

				for (Object item : collection) {
					Object convertedItem = convertPolarionValueToFeishu(item);
					if (convertedItem != null) {
						convertedList.add(convertedItem);
					}
				}

				return convertedList;
			}

			// 其他类型转换为字符串
			return polarionValue.toString();

		} catch (Exception e) {
			logger.warn("转换Polarion字段值时发生错误: " + polarionValue, e);
			return polarionValue.toString();
		}
	}



	/**
	 * 转换选项值
	 */
	private Object convertOptionValue(String optionValue) {
		try {
			// 移除option:前缀
			String cleanOptionValue = optionValue.startsWith("option:") ? optionValue.substring(7) : optionValue;

			// 处理多选项（用|分隔）
			if (cleanOptionValue.contains("|")) {
				String[] options = cleanOptionValue.split("\\|");
				List<String> optionList = new ArrayList<>();
				for (String option : options) {
					optionList.add(option.trim());
				}
				return optionList;
			}

			// 单选项
			return cleanOptionValue;

		} catch (Exception e) {
			logger.warn("转换选项值时发生错误: " + optionValue, e);
			return optionValue;
		}
	}

	/**
	 * 将飞书工作项转换为TransferItem
	 */
	private TransferItem convertWorkItemToTransferItem(WorkItemInfo workItem, Collection<String> fieldKeys) {
		try {
			String itemId = workItem.getID() != null ? workItem.getID().toString() : null;
			if (itemId == null) {
				logger.warn("工作项ID为空，跳过转换");
				return null;
			}

			Item item = new Item(itemId);

			// 设置基本属性，使用映射后的字段键
			if (workItem.getName() != null) {
				item.put(findMappedKey("name"), workItem.getName()); // name -> title
			}

			if (workItem.getWorkItemTypeKey() != null) {
				item.put(findMappedKey(KEY_WORK_ITEM_TYPE_KEY), workItem.getWorkItemTypeKey()); // work_item_type_key ->
																								// type
			}

			// 设置时间戳，使用映射后的字段键
			if (workItem.getCreatedAt() != null) {
				item.put(findMappedKey("start_time"), new Date(workItem.getCreatedAt() * 1000)); // start_time ->
																									// created
			}

			if (workItem.getUpdatedAt() != null) {
				item.put(findMappedKey("updated_at"), new Date(workItem.getUpdatedAt() * 1000)); // updated_at ->
																									// updated
			}

			// 设置创建者和更新者，使用映射后的字段键
			if (workItem.getCreatedBy() != null) {
				item.put(findMappedKey("owner"), workItem.getCreatedBy()); // owner -> author
			}

			if (workItem.getUpdatedBy() != null) {
				item.put(findMappedKey("updated_by"), workItem.getUpdatedBy()); // updated_by -> updatedBy
			}

			// 设置状态信息，使用映射后的字段键
			if (workItem.getWorkItemStatus() != null) {
				item.put(findMappedKey("work_item_status"), workItem.getWorkItemStatus()); // work_item_status -> status
			}

			if (workItem.getSubStage() != null) {
				item.put("sub_stage", workItem.getSubStage()); // 自定义字段保持原样
			}

			// 处理字段值
			if (workItem.getFields() != null) {
				for (FieldValuePair field : workItem.getFields()) {
					if (field.getFieldKey() != null && field.getFieldValue() != null) {
						// 处理附件字段（参考 JIRA 的实现）
						if ("attachments".equals(field.getFieldKey()) || "files".equals(field.getFieldKey())) {
							createAttachmentIdMap(field.getFieldValue(), field.getFieldKey());
						}

						// 根据字段类型处理字段值
						Object fieldValue = convertFieldValue(field);
						if (fieldValue != null) {
							// 对于系统字段进行映射，自定义字段保持原样
							// 这里我们无法直接判断是否为自定义字段，所以统一进行映射
							// 如果映射后的键与原键相同，说明是自定义字段或未映射的字段
							String mappedKey = findMappedKey(field.getFieldKey());
							item.put(mappedKey, fieldValue);
						}
					}
				}
			}

			// 设置项目链接（按需设置）
			if (fieldKeys == null || fieldKeys.contains(IProxy.KEY_ITEM_URL)) {
				String itemUrl = buildWorkItemUrl(workItem);
				if (itemUrl != null) {
					item.put(IProxy.KEY_ITEM_URL, itemUrl);
				}
			}

			// 过滤字段（如果指定了fieldKeys）
			if (fieldKeys != null && !fieldKeys.isEmpty()) {
				filterTransferItemFields(item, fieldKeys);
			}

			return item;

		} catch (Exception e) {
			logger.error("转换工作项时发生错误: " + workItem.getID(), e);
			return null;
		}
	}

	/**
	 * 转换字段值
	 */
	private Object convertFieldValue(FieldValuePair field) {
		try {
			Object value = field.getFieldValue();
			if (value == null) {
				return null;
			}

			// 根据字段类型进行转换
			String fieldKey = field.getFieldKey();

			// 处理特殊字段
			if ("description".equals(fieldKey)) {
				if (value instanceof String) {
					return value; // 普通描述字段保持原样
				} else if (value instanceof MultiTextDetail) {
					// 飞书富文本字段，转换为 FeishuRichText
					return FeishuRichTextHelper.fromMultiTextDetail((MultiTextDetail) value);
				}
			}

			// 处理 multi_text 字段（飞书富文本）
			if ("multi_text".equals(fieldKey) && value instanceof MultiTextDetail) {
				FeishuRichText richText = FeishuRichTextHelper.fromMultiTextDetail((MultiTextDetail) value);
				// 设置附件映射
				richText.setAttachmentIdNameMap(getAttachmentIdNameMap());
				return richText;
			}

			// 处理附件字段（参考 JIRA 的实现）
			if ("attachments".equals(fieldKey) || "files".equals(fieldKey)) {
				if (value instanceof Collection) {
					@SuppressWarnings("unchecked")
					Collection<Object> attachmentList = (Collection<Object>) value;
					Collection<FeishuAttachment> feishuAttachments = new ArrayList<>();

					for (Object attachmentObj : attachmentList) {
						try {
							FeishuAttachment feishuAttachment = FeishuAttachment.fromApiResponse(attachmentObj);
							feishuAttachments.add(feishuAttachment);

							// 更新附件映射
							attachmentIdNameMap.put(feishuAttachment.getFileId(), feishuAttachment.getFileName());
						} catch (Exception e) {
							logger.warn("解析附件对象失败: " + attachmentObj, e);
						}
					}

					return feishuAttachments;
				}
			}

			// 处理日期字段
			if (fieldKey.contains("date") || fieldKey.contains("time")) {
				if (value instanceof Number) {
					long timestamp = ((Number) value).longValue();
					// 飞书时间戳可能是秒或毫秒，自动判断
					if (timestamp < 10000000000L) { // 小于10位数，认为是秒级时间戳
						timestamp *= 1000;
					}
					return new Date(timestamp);
				} else if (value instanceof String) {
					// 尝试解析字符串格式的日期
					return parseDateString((String) value);
				}
			}

			// 处理数字字段
			if (value instanceof Number) {
				return value;
			}

			// 处理布尔字段
			if (value instanceof Boolean) {
				return value;
			}

			// 其他情况转换为字符串
			return value.toString();

		} catch (Exception e) {
			logger.warn("转换字段值时发生错误: " + field.getFieldKey(), e);
			return null;
		}
	}

	/**
	 * 构建工作项URL
	 */
	private String buildWorkItemUrl(WorkItemInfo workItem) {
		try {
			FeishuConnection connection = (FeishuConnection) configuration.getConnection();
			String baseUrl = connection.getServerUrl();
			if (baseUrl == null) {
				baseUrl = "https://project.feishu.cn";
			}

			// 构建飞书项目工作项URL
			return baseUrl + "/project/" + workItem.getProjectKey() + "/workitem/" + workItem.getID();

		} catch (Exception e) {
			logger.warn("构建工作项URL时发生错误", e);
			return null;
		}
	}

	/**
	 * 过滤TransferItem字段
	 */
	private void filterTransferItemFields(TransferItem item, Collection<String> fieldKeys) {
		if (fieldKeys == null || fieldKeys.isEmpty()) {
			return; // 没有指定字段过滤，保留所有字段
		}

		try {
			// 创建反向映射：从映射后的字段到原始字段的映射
			Set<String> allowedMappedFields = new HashSet<>();

			for (String fieldKey : fieldKeys) {
				// 添加原始字段
				allowedMappedFields.add(fieldKey);
				// 添加映射后的字段
				String mappedKey = findMappedKey(fieldKey);
				if (!mappedKey.equals(fieldKey)) {
					allowedMappedFields.add(mappedKey);
				}
			}

			Map<String, Object> values = item.getValues();

			if (values != null) {
				// 创建新的字段映射，只包含允许的字段
				Map<String, Object> filteredFields = new HashMap<>();

				for (Map.Entry<String, Object> entry : values.entrySet()) {
					String fieldKey = entry.getKey();

					// 保留在允许列表中的字段（包括映射后的字段）
					if (allowedMappedFields.contains(fieldKey)) {
						filteredFields.put(fieldKey, entry.getValue());
					}
					// 保留基本系统字段
					else if (isSystemField(fieldKey)) {
						filteredFields.put(fieldKey, entry.getValue());
					}
				}

				// 清空原有字段并设置过滤后的字段
				values.clear();
				values.putAll(filteredFields);
			}

			logger.debug("字段过滤完成，保留字段数量: " + (values != null ? values.size() : 0));

		} catch (Exception e) {
			logger.warn("字段过滤时发生错误", e);
		}
	}

	/**
	 * 判断是否为系统字段
	 */
	private boolean isSystemField(String fieldKey) {
		if (fieldKey == null) {
			return false;
		}

		// 系统字段列表（包括映射后的字段）
		Set<String> systemFields = Set.of(KEY_WORK_ITEM_ID, IProxy.KEY_TYPE, IProxy.KEY_ITEM_URL, "id", "key",
				// 映射后的标准字段
				"title", "description", "status", "priority", "assignee", "author", "created", "updated", "updatedBy",
				"dueDate", "reporter", "categories", "subscribers",
				// 原始字段
				"created_at", "updated_at", "creator", "updated_by");

		return systemFields.contains(fieldKey);
	}

	/**
	 * 验证工作项类型是否有效
	 */
	private boolean isValidWorkItemType(String workItemType) {
		if (workItemType == null || workItemType.trim().isEmpty()) {
			return false;
		}

		try {
			// 获取所有可用的工作项类型
			Collection<Option> availableTypes = getDefinedTypes();

			if (availableTypes != null) {
				for (Option option : availableTypes) {
					if (workItemType.equals(option.getId())) {
						return true;
					}
				}
			}

			// 如果无法获取类型列表，允许常见的类型
			Set<String> commonTypes = Set.of("task", "story", "bug", "epic", "feature", "requirement", "test_case",
					"issue", "subtask");

			return commonTypes.contains(workItemType.toLowerCase());

		} catch (Exception e) {
			logger.warn("验证工作项类型时发生错误: " + workItemType, e);
			// 发生错误时，允许常见类型
			return Set.of("task", "story", "bug", "epic", "feature").contains(workItemType.toLowerCase());
		}
	}

	/**
	 * 验证字段值是否有效
	 */
	private boolean isValidFieldValue(String fieldKey, Object fieldValue) {
		if (fieldKey == null || fieldValue == null) {
			return false;
		}

		try {
			// 检查字段值长度限制
			if (fieldValue instanceof String) {
				String strValue = (String) fieldValue;

				// 标题字段长度限制
				if ("name".equals(fieldKey) || "title".equals(fieldKey)) {
					return strValue.length() <= 255;
				}

				// 描述字段长度限制
				if ("description".equals(fieldKey)) {
					return strValue.length() <= 65535;
				}

				// 一般字符串字段长度限制
				return strValue.length() <= 1000;
			}

			// 数字字段范围检查
			if (fieldValue instanceof Number) {
				Number numValue = (Number) fieldValue;

				// 检查是否在合理范围内
				if (fieldValue instanceof Integer || fieldValue instanceof Long) {
					long longValue = numValue.longValue();
					return longValue >= Integer.MIN_VALUE && longValue <= Integer.MAX_VALUE;
				}

				if (fieldValue instanceof Float || fieldValue instanceof Double) {
					double doubleValue = numValue.doubleValue();
					return !Double.isInfinite(doubleValue) && !Double.isNaN(doubleValue);
				}
			}

			// 日期字段检查
			if (fieldValue instanceof Date) {
				Date dateValue = (Date) fieldValue;
				// 检查日期是否在合理范围内（1970-2100年）
				long timestamp = dateValue.getTime();
				return timestamp > 0 && timestamp < 4102444800000L; // 2100年1月1日
			}

			return true;

		} catch (Exception e) {
			logger.warn("验证字段值时发生错误: " + fieldKey + " = " + fieldValue, e);
			return false;
		}
	}

	/**
	 * 解析日期字符串
	 */
	private Date parseDateString(String dateStr) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			return null;
		}

		try {
			// 尝试解析ISO 8601格式
			if (dateStr.contains("T")) {
				return java.time.Instant.parse(dateStr).toEpochMilli() > 0 ? Date.from(java.time.Instant.parse(dateStr))
						: null;
			}

			// 尝试解析时间戳字符串
			try {
				long timestamp = Long.parseLong(dateStr);
				if (timestamp < 10000000000L) { // 秒级时间戳
					timestamp *= 1000;
				}
				return new Date(timestamp);
			} catch (NumberFormatException e) {
				// 不是时间戳格式，继续尝试其他格式
			}

			// 尝试解析常见日期格式
			String[] patterns = { "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSS",
					"yyyy-MM-dd", "MM/dd/yyyy", "dd/MM/yyyy" };

			for (String pattern : patterns) {
				try {
					java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(pattern);
					return sdf.parse(dateStr);
				} catch (java.text.ParseException e) {
					// 继续尝试下一个格式
				}
			}

			logger.warn("无法解析日期字符串: " + dateStr);
			return null;

		} catch (Exception e) {
			logger.warn("解析日期字符串时发生错误: " + dateStr, e);
			return null;
		}
	}

	public Client getFeishuClient() {
		return getClient();
	}

	@NotNull
	public FeishuProxyConfiguration getConfiguration() {
		return configuration;
	}



}
